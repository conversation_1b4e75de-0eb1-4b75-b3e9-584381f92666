package com.nacos.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;


/**
 * 统一任务系统配置类
 * 
 * 基于策略模式+工厂模式+Redis消息队列的统一任务处理架构配置
 * 支持视频翻译、音频生成、视频编辑三种任务类型的统一调度和处理
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务系统架构
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "universal-task")
@Validated
public class UniversalTaskConfig {

    /**
     * 任务系统总开关
     */
    @NotNull(message = "任务系统开关不能为空")
    private Boolean enabled = true;

    /**
     * 视频翻译任务专用配置
     */
    @Valid
    private VideoTranslateConfig videoTranslate = new VideoTranslateConfig();

    /**
     * Redis消息队列配置
     */
    @Valid
    private RedisConfig redis = new RedisConfig();

    /**
     * 任务处理器配置
     */
    @Valid
    private ProcessorsConfig processors = new ProcessorsConfig();

    /**
     * 兜底定时任务配置
     */
    @Valid
    private FallbackSchedulerConfig fallbackScheduler = new FallbackSchedulerConfig();

    /**
     * 任务状态管理配置
     */
    @Valid
    private StatusConfig status = new StatusConfig();

    /**
     * 监控和日志配置
     */
    @Valid
    private MonitoringConfig monitoring = new MonitoringConfig();

    /**
     * 任务超时配置
     */
    @Valid
    private TimeoutConfig timeout = new TimeoutConfig();

    /**
     * Redis消息队列配置
     */
    @Data
    public static class RedisConfig {
        /**
         * 队列名称配置
         */
        @Valid
        private QueuesConfig queues = new QueuesConfig();

        /**
         * 消息序列化配置
         */
        @Valid
        private SerializationConfig serialization = new SerializationConfig();

        /**
         * 消息重试策略
         */
        @Valid
        private RetryConfig retry = new RetryConfig();

        /**
         * 死信队列配置
         */
        @Valid
        private DeadLetterConfig deadLetter = new DeadLetterConfig();
    }

    /**
     * 队列名称配置
     */
    @Data
    public static class QueuesConfig {
        @NotBlank(message = "视频翻译队列名称不能为空")
        private String videoTranslate = "TOPIC_VIDEO_TRANSLATE";

        @NotBlank(message = "音频生成队列名称不能为空")
        private String audioGenerate = "TOPIC_AUDIO_GENERATE";

        @NotBlank(message = "视频编辑队列名称不能为空")
        private String videoEdit = "TOPIC_VIDEO_EDIT";
    }

    /**
     * 消息序列化配置
     */
    @Data
    public static class SerializationConfig {
        @NotBlank(message = "序列化类型不能为空")
        private String type = "JSON";

        @NotBlank(message = "字符编码不能为空")
        private String charset = "UTF-8";
    }

    /**
     * 消息重试策略配置
     */
    @Data
    public static class RetryConfig {
        @Min(value = 1, message = "最大重试次数必须大于0")
        private Integer maxAttempts = 3;

        @Min(value = 100, message = "初始重试间隔必须大于100毫秒")
        private Long initialInterval = 1000L;

        @Min(value = 1, message = "重试间隔倍数必须大于1")
        private Double multiplier = 2.0;

        @Min(value = 1000, message = "最大重试间隔必须大于1000毫秒")
        private Long maxInterval = 10000L;
    }

    /**
     * 死信队列配置
     */
    @Data
    public static class DeadLetterConfig {
        private Boolean enabled = true;

        @NotBlank(message = "死信队列后缀不能为空")
        private String queueSuffix = "_DLQ";

        @Min(value = 60000, message = "TTL必须大于60秒")
        private Long ttl = 86400000L; // 24小时
    }

    /**
     * 任务处理器配置
     */
    @Data
    public static class ProcessorsConfig {
        /**
         * 视频翻译处理器专用配置
         */
        @Valid
        private VideoTranslateProcessorConfig videoTranslate = new VideoTranslateProcessorConfig();

        /**
         * 处理器健康检查配置
         */
        @Valid
        private HealthCheckConfig healthCheck = new HealthCheckConfig();

        /**
         * 处理器注册配置
         */
        @Valid
        private RegistrationConfig registration = new RegistrationConfig();
    }

    /**
     * 处理器健康检查配置
     */
    @Data
    public static class HealthCheckConfig {
        private Boolean enabled = true;

        @Min(value = 5000, message = "健康检查间隔必须大于5秒")
        private Long interval = 30000L;

        @Min(value = 1000, message = "健康检查超时必须大于1秒")
        private Long timeout = 5000L;
    }

    /**
     * 处理器注册配置
     */
    @Data
    public static class RegistrationConfig {
        private Boolean autoScan = true;

        @NotBlank(message = "扫描包路径不能为空")
        private String basePackages = "com.nacos.service.processor";
    }

    /**
     * 兜底定时任务配置
     */
    @Data
    public static class FallbackSchedulerConfig {
        private Boolean enabled = true;

        @Min(value = 10000, message = "执行间隔必须大于10秒")
        private Long fixedDelay = 60000L;

        @Min(value = 0, message = "初始延迟不能为负数")
        private Long initialDelay = 30000L;

        @Min(value = 1, message = "批量大小必须大于0")
        private Integer batchSize = 50;

        @Min(value = 1, message = "最大重试时间必须大于1小时")
        private Integer maxRetryHours = 24;
    }

    /**
     * 任务状态管理配置
     */
    @Data
    public static class StatusConfig {
        /**
         * 状态同步配置
         */
        @Valid
        private SyncConfig sync = new SyncConfig();

        /**
         * 状态映射配置
         */
        @Valid
        private MappingConfig mapping = new MappingConfig();
    }

    /**
     * 状态同步配置
     */
    @Data
    public static class SyncConfig {
        private Boolean enabled = true;

        @Min(value = 1, message = "批量同步大小必须大于0")
        private Integer batchSize = 50;

        // 基础扫描频率（调度器执行频率）
        @Min(value = 30000, message = "基础扫描频率必须大于30秒")
        private Long baseScanRate = 30000L;  // 30秒

        // 分阶段查询配置
        @Valid
        private StageConfig stages = new StageConfig();

        // API限流配置
        @Valid
        private RateLimitConfig rateLimit = new RateLimitConfig();
    }

    /**
     * 分阶段查询配置
     */
    @Data
    public static class StageConfig {
        // 阶段1：任务提交后0-5分钟，每30秒查询一次
        @Min(value = 30000, message = "阶段1查询间隔必须大于30秒")
        private Long stage1IntervalMs = 30000L;  // 30秒

        @Min(value = 300000, message = "阶段1结束时间必须大于5分钟")
        private Long stage1EndMs = 300000L;      // 5分钟

        // 阶段2：5分钟以上，每1分钟查询一次
        @Min(value = 60000, message = "阶段2查询间隔必须大于1分钟")
        private Long stage2IntervalMs = 60000L;  // 1分钟

        @Min(value = 1800000, message = "阶段2结束时间必须大于30分钟")
        private Long stage2EndMs = 1800000L;     // 30分钟（超过此时间继续使用阶段2间隔）
    }

    /**
     * API限流配置
     */
    @Data
    public static class RateLimitConfig {
        // 全局API调用限制
        @Min(value = 1, message = "每分钟最大调用次数必须大于0")
        private Integer maxCallsPerMinute = 150;

        // 单个服务商限制
        @Min(value = 1, message = "单服务商最大并发必须大于0")
        private Integer maxConcurrentPerProvider = 5;

        // 缓存时间（避免重复查询）
        @Min(value = 30000, message = "缓存时间必须大于30秒")
        private Long cacheTimeMs = 60000L;  // 1分钟
    }

    /**
     * 状态映射配置
     */
    @Data
    public static class MappingConfig {
        private Boolean strictMode = false;
    }

    /**
     * 监控和日志配置
     */
    @Data
    public static class MonitoringConfig {
        /**
         * 性能监控配置
         */
        @Valid
        private MetricsConfig metrics = new MetricsConfig();

        /**
         * 任务执行日志配置
         */
        @Valid
        private LoggingConfig logging = new LoggingConfig();
    }

    /**
     * 性能监控配置
     */
    @Data
    public static class MetricsConfig {
        private Boolean enabled = true;

        @Min(value = 10000, message = "指标导出间隔必须大于10秒")
        private Long exportInterval = 60000L;
    }

    /**
     * 任务执行日志配置
     */
    @Data
    public static class LoggingConfig {
        private Boolean enabled = true;

        @NotBlank(message = "日志级别不能为空")
        private String level = "INFO";

        private Boolean includeContext = true;
        private Boolean includeStackTrace = false;
    }

    /**
     * 任务超时配置
     */
    @Data
    public static class TimeoutConfig {
        @Min(value = 60000, message = "默认超时时间必须大于1分钟")
        private Long defaultTimeout = 1800000L; // 30分钟

        @Min(value = 60000, message = "视频翻译超时时间必须大于1分钟")
        private Long videoTranslate = 1800000L; // 30分钟

        @Min(value = 60000, message = "音频生成超时时间必须大于1分钟")
        private Long audioGenerate = 300000L; // 5分钟

        @Min(value = 60000, message = "视频编辑超时时间必须大于1分钟")
        private Long videoEdit = 3600000L; // 60分钟
    }

    // ==================== 业务方法 ====================

    /**
     * 获取指定任务类型的队列名称
     */
    public String getQueueName(String taskType) {
        switch (taskType.toUpperCase()) {
            case "VIDEO_TRANSLATE":
                return redis.queues.videoTranslate;
            case "AUDIO_GENERATE":
                return redis.queues.audioGenerate;
            case "VIDEO_EDIT":
                return redis.queues.videoEdit;
            default:
                throw new IllegalArgumentException("不支持的任务类型: " + taskType);
        }
    }

    /**
     * 获取指定任务类型的超时时间
     */
    public Long getTimeout(String taskType) {
        switch (taskType.toUpperCase()) {
            case "VIDEO_TRANSLATE":
                return timeout.videoTranslate;
            case "AUDIO_GENERATE":
                return timeout.audioGenerate;
            case "VIDEO_EDIT":
                return timeout.videoEdit;
            default:
                return timeout.defaultTimeout;
        }
    }

    /**
     * 验证配置完整性
     */
    public boolean isValid() {
        return enabled != null && enabled &&
               redis != null &&
               processors != null &&
               fallbackScheduler != null &&
               status != null &&
               monitoring != null &&
               timeout != null;
    }

    /**
     * 视频翻译任务专用配置
     */
    @Data
    public static class VideoTranslateConfig {
        /**
         * 视频翻译统一任务系统开关，默认false确保向后兼容
         */
        private Boolean enabled = false;

        /**
         * 兜底机制开关，当统一系统失败时使用原系统
         */
        private Boolean fallbackEnabled = true;
    }

    /**
     * 视频翻译处理器专用配置
     */
    @Data
    public static class VideoTranslateProcessorConfig {
        /**
         * 视频翻译处理器开关
         */
        private Boolean enabled = true;

        /**
         * 处理超时时间(分钟)
         */
        @Min(value = 1, message = "超时时间必须大于1分钟")
        private Integer timeoutMinutes = 30;

        /**
         * 最大并发处理数
         */
        @Min(value = 1, message = "最大并发数必须大于0")
        private Integer maxConcurrent = 5;

        /**
         * 重试次数
         */
        @Min(value = 0, message = "重试次数不能为负数")
        private Integer retryAttempts = 3;
    }
}
