package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.po.DigitalVoiceSystemPO;
import com.nacos.entity.po.DigitalVoiceTagRelationPO;
import com.nacos.entity.vo.*;
import com.nacos.mapper.DigitalVoiceSystemMapper;
import com.nacos.mapper.DigitalVoiceTagRelationMapper;
import com.nacos.service.DigitalVoiceSystemService;
import com.nacos.service.DigitalVoiceClassifyTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统音色服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVoiceSystemServiceImpl extends ServiceImpl<DigitalVoiceSystemMapper, DigitalVoiceSystemPO> 
        implements DigitalVoiceSystemService {

    private final DigitalVoiceTagRelationMapper digitalVoiceTagRelationMapper;
    private final DigitalVoiceClassifyTagService digitalVoiceClassifyTagService;

    /**
     * 多维度查询音色
     */
    @Override
    public VoiceMultiDimensionQueryVO multiDimensionQuery(
            Map<String, List<String>> dimensionConditions,
            String keyword,
            List<String> providers,
            List<String> languages,
            List<String> genders,
            List<Long> syncIds,
            List<String> thirdPartyVoiceIds,
            String queryLogic,
            Integer currentPage,
            Integer pageSize) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<DigitalVoiceSystemPO> queryWrapper = buildQueryWrapper(
                dimensionConditions, keyword, providers, languages, genders, syncIds, thirdPartyVoiceIds, queryLogic);
            
            // 分页查询
            Page<DigitalVoiceSystemPO> page = new Page<>(
                currentPage != null ? currentPage : 1,
                pageSize != null ? pageSize : 20
            );
            
            Page<DigitalVoiceSystemPO> resultPage = this.page(page, queryWrapper);
            
            // 转换为VO并加载标签信息
            List<DigitalVoiceSystemVO> voiceList = resultPage.getRecords().stream()
                .map(this::convertToVOWithTags)
                .collect(Collectors.toList());
            
            // 构建查询结果
            VoiceMultiDimensionQueryVO result = new VoiceMultiDimensionQueryVO();
            
            // 设置查询条件摘要
            VoiceMultiDimensionQueryVO.VoiceQuerySummaryVO querySummary = new VoiceMultiDimensionQueryVO.VoiceQuerySummaryVO();
            querySummary.setDimensionConditions(dimensionConditions);
            querySummary.setKeyword(keyword);
            querySummary.setProviders(providers);
            querySummary.setLanguages(languages);
            querySummary.setGenders(genders);
            querySummary.setQueryLogic(queryLogic != null ? queryLogic : "AND");
            result.setQuerySummary(querySummary);
            
            // 设置音色列表
            result.setVoiceList(voiceList);
            
            // 设置分页信息
            VoiceMultiDimensionQueryVO.VoicePaginationVO pagination = new VoiceMultiDimensionQueryVO.VoicePaginationVO();
            pagination.setCurrentPage(Math.toIntExact(resultPage.getCurrent()));
            pagination.setPageSize(Math.toIntExact(resultPage.getSize()));
            pagination.setTotalCount(resultPage.getTotal());
            pagination.setTotalPages(Math.toIntExact(resultPage.getPages()));
            pagination.setHasNext(resultPage.hasNext());
            pagination.setHasPrevious(resultPage.hasPrevious());
            result.setPagination(pagination);
            
            // 设置统计信息（简化版本）
            result.setDimensionStats(new ArrayList<>());
            result.setTagStats(new HashMap<>());
            
            // 设置性能信息
            long endTime = System.currentTimeMillis();
            VoiceMultiDimensionQueryVO.VoiceQueryPerformanceVO performance = new VoiceMultiDimensionQueryVO.VoiceQueryPerformanceVO();
            performance.setQueryTimeMs(endTime - startTime);
            performance.setDbQueryCount(1);
            performance.setCacheHitRate(0.0);
            performance.setComplexityLevel("MEDIUM");
            result.setPerformance(performance);
            
            return result;
        } catch (Exception e) {
            log.error("多维度查询音色失败", e);
            throw new RuntimeException("查询音色失败", e);
        }
    }

    /**
     * 根据音色ID获取音色详情
     */
    @Override
    public DigitalVoiceSystemVO getVoiceDetail(String voiceId) {
        if (!StringUtils.hasText(voiceId)) {
            return null;
        }
        
        try {
            DigitalVoiceSystemPO voice = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getVoiceId, voiceId)
                    .eq(DigitalVoiceSystemPO::getStatus, 1)
            );
            
            return voice != null ? convertToVOWithTags(voice) : null;
        } catch (Exception e) {
            log.error("获取音色详情失败，voiceId: {}", voiceId, e);
            throw new RuntimeException("获取音色详情失败", e);
        }
    }

    /**
     * 根据音色ID列表获取音色信息
     */
    @Override
    public List<DigitalVoiceSystemVO> getVoicesByIds(List<String> voiceIds) {
        if (voiceIds == null || voiceIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            List<DigitalVoiceSystemPO> voices = this.list(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .in(DigitalVoiceSystemPO::getVoiceId, voiceIds)
                    .eq(DigitalVoiceSystemPO::getStatus, 1)
            );
            
            return voices.stream()
                .map(this::convertToVOWithTags)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据ID列表获取音色信息失败，voiceIds: {}", voiceIds, e);
            throw new RuntimeException("获取音色信息失败", e);
        }
    }

    /**
     * 获取推荐音色列表
     */
    @Override
    public List<DigitalVoiceSystemVO> getRecommendedVoices(Integer limit) {
        try {
            LambdaQueryWrapper<DigitalVoiceSystemPO> queryWrapper = new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                .eq(DigitalVoiceSystemPO::getStatus, 1)
                .eq(DigitalVoiceSystemPO::getIsRecommended, 1)
                .orderByDesc(DigitalVoiceSystemPO::getSortWeight)
                .orderByDesc(DigitalVoiceSystemPO::getCreatedTime);
            
            if (limit != null && limit > 0) {
                queryWrapper.last("LIMIT " + limit);
            }
            
            List<DigitalVoiceSystemPO> voices = this.list(queryWrapper);
            
            return voices.stream()
                .map(this::convertToVOWithTags)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取推荐音色列表失败", e);
            throw new RuntimeException("获取推荐音色列表失败", e);
        }
    }

    /**
     * 根据供应商获取音色列表
     */
    @Override
    public Page<DigitalVoiceSystemVO> getVoicesByProvider(String provider, Integer currentPage, Integer pageSize) {
        if (!StringUtils.hasText(provider)) {
            throw new IllegalArgumentException("供应商标识不能为空");
        }
        
        try {
            Page<DigitalVoiceSystemPO> page = new Page<>(
                currentPage != null ? currentPage : 1,
                pageSize != null ? pageSize : 20
            );
            
            LambdaQueryWrapper<DigitalVoiceSystemPO> queryWrapper = new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                .eq(DigitalVoiceSystemPO::getProvider, provider)
                .eq(DigitalVoiceSystemPO::getStatus, 1)
                .orderByDesc(DigitalVoiceSystemPO::getSortWeight)
                .orderByDesc(DigitalVoiceSystemPO::getCreatedTime);
            
            Page<DigitalVoiceSystemPO> resultPage = this.page(page, queryWrapper);
            
            // 转换为VO分页结果
            Page<DigitalVoiceSystemVO> voPage = new Page<>();
            BeanUtils.copyProperties(resultPage, voPage);
            
            List<DigitalVoiceSystemVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVOWithTags)
                .collect(Collectors.toList());
            voPage.setRecords(voList);
            
            return voPage;
        } catch (Exception e) {
            log.error("根据供应商获取音色列表失败，provider: {}", provider, e);
            throw new RuntimeException("获取音色列表失败", e);
        }
    }

    /**
     * 根据标签获取音色列表
     */
    @Override
    public Page<DigitalVoiceSystemVO> getVoicesByTags(List<Long> tagIds, String queryLogic, Integer currentPage, Integer pageSize) {
        if (tagIds == null || tagIds.isEmpty()) {
            return new Page<>();
        }
        
        try {
            // 根据查询逻辑获取音色ID列表
            List<String> voiceIds = getVoiceIdsByTags(tagIds, queryLogic);
            
            if (voiceIds.isEmpty()) {
                return new Page<>();
            }
            
            Page<DigitalVoiceSystemPO> page = new Page<>(
                currentPage != null ? currentPage : 1,
                pageSize != null ? pageSize : 20
            );
            
            LambdaQueryWrapper<DigitalVoiceSystemPO> queryWrapper = new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                .in(DigitalVoiceSystemPO::getVoiceId, voiceIds)
                .eq(DigitalVoiceSystemPO::getStatus, 1)
                .orderByDesc(DigitalVoiceSystemPO::getSortWeight)
                .orderByDesc(DigitalVoiceSystemPO::getCreatedTime);
            
            Page<DigitalVoiceSystemPO> resultPage = this.page(page, queryWrapper);
            
            // 转换为VO分页结果
            Page<DigitalVoiceSystemVO> voPage = new Page<>();
            BeanUtils.copyProperties(resultPage, voPage);
            
            List<DigitalVoiceSystemVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVOWithTags)
                .collect(Collectors.toList());
            voPage.setRecords(voList);
            
            return voPage;
        } catch (Exception e) {
            log.error("根据标签获取音色列表失败，tagIds: {}", tagIds, e);
            throw new RuntimeException("获取音色列表失败", e);
        }
    }

    /**
     * 根据标签编码获取音色列表
     */
    @Override
    public Page<DigitalVoiceSystemVO> getVoicesByTagCodes(List<String> tagCodes, String queryLogic, Integer currentPage, Integer pageSize) {
        if (tagCodes == null || tagCodes.isEmpty()) {
            return new Page<>();
        }

        try {
            // 根据查询逻辑获取音色ID列表
            List<String> voiceIds = getVoiceIdsByTagCodes(tagCodes, queryLogic);

            if (voiceIds.isEmpty()) {
                return new Page<>();
            }

            Page<DigitalVoiceSystemPO> page = new Page<>(
                currentPage != null ? currentPage : 1,
                pageSize != null ? pageSize : 20
            );

            LambdaQueryWrapper<DigitalVoiceSystemPO> queryWrapper = new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                .in(DigitalVoiceSystemPO::getVoiceId, voiceIds)
                .eq(DigitalVoiceSystemPO::getStatus, 1)
                .orderByDesc(DigitalVoiceSystemPO::getSortWeight)
                .orderByDesc(DigitalVoiceSystemPO::getCreatedTime);

            Page<DigitalVoiceSystemPO> resultPage = this.page(page, queryWrapper);

            // 转换为VO分页结果
            Page<DigitalVoiceSystemVO> voPage = new Page<>();
            BeanUtils.copyProperties(resultPage, voPage);

            List<DigitalVoiceSystemVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVOWithTags)
                .collect(Collectors.toList());
            voPage.setRecords(voList);

            return voPage;
        } catch (Exception e) {
            log.error("根据标签编码获取音色列表失败，tagCodes: {}", tagCodes, e);
            throw new RuntimeException("获取音色列表失败", e);
        }
    }

    /**
     * 搜索音色
     */
    @Override
    public Page<DigitalVoiceSystemVO> searchVoices(String keyword, Integer currentPage, Integer pageSize) {
        try {
            Page<DigitalVoiceSystemPO> page = new Page<>(
                currentPage != null ? currentPage : 1,
                pageSize != null ? pageSize : 20
            );

            LambdaQueryWrapper<DigitalVoiceSystemPO> queryWrapper = new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                .eq(DigitalVoiceSystemPO::getStatus, 1);

            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> wrapper
                    .like(DigitalVoiceSystemPO::getVoiceName, keyword)
                    .or()
                    .like(DigitalVoiceSystemPO::getDescription, keyword)
                );
            }

            queryWrapper.orderByDesc(DigitalVoiceSystemPO::getSortWeight)
                .orderByDesc(DigitalVoiceSystemPO::getCreatedTime);

            Page<DigitalVoiceSystemPO> resultPage = this.page(page, queryWrapper);

            // 转换为VO分页结果
            Page<DigitalVoiceSystemVO> voPage = new Page<>();
            BeanUtils.copyProperties(resultPage, voPage);

            List<DigitalVoiceSystemVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVOWithTags)
                .collect(Collectors.toList());
            voPage.setRecords(voList);

            return voPage;
        } catch (Exception e) {
            log.error("搜索音色失败，keyword: {}", keyword, e);
            throw new RuntimeException("搜索音色失败", e);
        }
    }

    /**
     * 更新音色标签关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVoiceTagRelations(String voiceId, List<Long> tagIds) {
        if (!StringUtils.hasText(voiceId)) {
            throw new IllegalArgumentException("音色ID不能为空");
        }

        try {
            // 删除原有关联
            digitalVoiceTagRelationMapper.delete(
                new LambdaQueryWrapper<DigitalVoiceTagRelationPO>()
                    .eq(DigitalVoiceTagRelationPO::getVoiceId, voiceId)
            );

            // 添加新的关联
            if (tagIds != null && !tagIds.isEmpty()) {
                List<VoiceClassifyTagVO> tags = digitalVoiceClassifyTagService.getTagsByIds(tagIds);

                List<DigitalVoiceTagRelationPO> relations = tags.stream()
                    .map(tag -> {
                        DigitalVoiceTagRelationPO relation = new DigitalVoiceTagRelationPO();
                        relation.setVoiceId(voiceId);
                        relation.setTagCode(tag.getTagCode());
                        relation.setDimensionCode(tag.getDimensionCode());
                        return relation;
                    })
                    .collect(Collectors.toList());

                relations.forEach(digitalVoiceTagRelationMapper::insert);
            }
        } catch (Exception e) {
            log.error("更新音色标签关联失败，voiceId: {}, tagIds: {}", voiceId, tagIds, e);
            throw new RuntimeException("更新音色标签关联失败", e);
        }
    }

    /**
     * 通过标签编码更新音色标签关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVoiceTagRelationsByCodes(String voiceId, List<String> tagCodes) {
        if (!StringUtils.hasText(voiceId)) {
            throw new IllegalArgumentException("音色ID不能为空");
        }

        try {
            // 删除原有关联
            digitalVoiceTagRelationMapper.delete(
                new LambdaQueryWrapper<DigitalVoiceTagRelationPO>()
                    .eq(DigitalVoiceTagRelationPO::getVoiceId, voiceId)
            );

            // 添加新的关联
            if (tagCodes != null && !tagCodes.isEmpty()) {
                List<DigitalVoiceTagRelationPO> relations = tagCodes.stream()
                    .map(tagCode -> {
                        // 根据tagCode获取标签信息以获取dimensionCode
                        VoiceClassifyTagVO tag = digitalVoiceClassifyTagService.getTagByCode(tagCode);
                        if (tag == null) {
                            throw new IllegalArgumentException("标签编码不存在：" + tagCode);
                        }

                        DigitalVoiceTagRelationPO relation = new DigitalVoiceTagRelationPO();
                        relation.setVoiceId(voiceId);
                        relation.setTagCode(tagCode);
                        relation.setDimensionCode(tag.getDimensionCode());
                        return relation;
                    })
                    .collect(Collectors.toList());

                relations.forEach(digitalVoiceTagRelationMapper::insert);
            }
        } catch (Exception e) {
            log.error("通过标签编码更新音色标签关联失败，voiceId: {}, tagCodes: {}", voiceId, tagCodes, e);
            throw new RuntimeException("更新音色标签关联失败", e);
        }
    }

    /**
     * 批量更新音色标签关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateVoiceTagRelations(Map<String, List<Long>> voiceTagMap) {
        if (voiceTagMap == null || voiceTagMap.isEmpty()) {
            return;
        }

        try {
            for (Map.Entry<String, List<Long>> entry : voiceTagMap.entrySet()) {
                updateVoiceTagRelations(entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            log.error("批量更新音色标签关联失败", e);
            throw new RuntimeException("批量更新音色标签关联失败", e);
        }
    }

    /**
     * 创建音色
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DigitalVoiceSystemVO createVoice(DigitalVoiceSystemVO voiceSystemVO) {
        if (voiceSystemVO == null || !StringUtils.hasText(voiceSystemVO.getVoiceId())) {
            throw new IllegalArgumentException("音色信息不能为空");
        }

        try {
            DigitalVoiceSystemPO voice = new DigitalVoiceSystemPO();
            BeanUtils.copyProperties(voiceSystemVO, voice);

            // 设置默认值
            if (voice.getStatus() == null) {
                voice.setStatus(1);
            }
            if (voice.getIsRecommended() == null) {
                voice.setIsRecommended(0);
            }
            if (voice.getSortWeight() == null) {
                voice.setSortWeight(0);
            }

            boolean success = this.save(voice);
            if (!success) {
                throw new RuntimeException("创建音色失败");
            }

            // 更新标签关联
            if (voiceSystemVO.getTagList() != null && !voiceSystemVO.getTagList().isEmpty()) {
                List<Long> tagIds = voiceSystemVO.getTagList().stream()
                    .map(VoiceClassifyTagVO::getId)
                    .collect(Collectors.toList());
                updateVoiceTagRelations(voice.getVoiceId(), tagIds);
            }

            return convertToVOWithTags(voice);
        } catch (Exception e) {
            log.error("创建音色失败，voiceId: {}", voiceSystemVO.getVoiceId(), e);
            throw new RuntimeException("创建音色失败", e);
        }
    }

    /**
     * 更新音色信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DigitalVoiceSystemVO updateVoice(String voiceId, DigitalVoiceSystemVO voiceSystemVO) {
        if (!StringUtils.hasText(voiceId) || voiceSystemVO == null) {
            throw new IllegalArgumentException("音色ID和音色信息不能为空");
        }

        try {
            DigitalVoiceSystemPO voice = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getVoiceId, voiceId)
            );

            if (voice == null) {
                throw new IllegalArgumentException("音色不存在：" + voiceId);
            }

            // 更新字段
            if (StringUtils.hasText(voiceSystemVO.getVoiceName())) {
                voice.setVoiceName(voiceSystemVO.getVoiceName());
            }
            if (StringUtils.hasText(voiceSystemVO.getDescription())) {
                voice.setDescription(voiceSystemVO.getDescription());
            }
            if (StringUtils.hasText(voiceSystemVO.getLanguage())) {
                voice.setLanguage(voiceSystemVO.getLanguage());
            }
            if (StringUtils.hasText(voiceSystemVO.getGender())) {
                voice.setGender(voiceSystemVO.getGender());
            }
            if (StringUtils.hasText(voiceSystemVO.getDemoAudioUrl())) {
                voice.setDemoAudioUrl(voiceSystemVO.getDemoAudioUrl());
            }
            if (voiceSystemVO.getSortWeight() != null) {
                voice.setSortWeight(voiceSystemVO.getSortWeight());
            }
            if (voiceSystemVO.getStatus() != null) {
                voice.setStatus(voiceSystemVO.getStatus());
            }
            if (voiceSystemVO.getIsRecommended() != null) {
                voice.setIsRecommended(voiceSystemVO.getIsRecommended());
            }

            boolean success = this.updateById(voice);
            if (!success) {
                throw new RuntimeException("更新音色失败");
            }

            // 更新标签关联
            if (voiceSystemVO.getTagList() != null) {
                List<Long> tagIds = voiceSystemVO.getTagList().stream()
                    .map(VoiceClassifyTagVO::getId)
                    .collect(Collectors.toList());
                updateVoiceTagRelations(voiceId, tagIds);
            }

            return convertToVOWithTags(voice);
        } catch (Exception e) {
            log.error("更新音色失败，voiceId: {}", voiceId, e);
            throw new RuntimeException("更新音色失败", e);
        }
    }

    /**
     * 删除音色（逻辑删除）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVoice(String voiceId) {
        if (!StringUtils.hasText(voiceId)) {
            throw new IllegalArgumentException("音色ID不能为空");
        }

        try {
            DigitalVoiceSystemPO voice = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getVoiceId, voiceId)
            );

            if (voice == null) {
                return false;
            }

            // 设置为下架状态
            voice.setStatus(0);
            return this.updateById(voice);
        } catch (Exception e) {
            log.error("删除音色失败，voiceId: {}", voiceId, e);
            throw new RuntimeException("删除音色失败", e);
        }
    }

    /**
     * 更新音色状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVoiceStatus(String voiceId, Integer status) {
        if (!StringUtils.hasText(voiceId) || status == null) {
            throw new IllegalArgumentException("音色ID和状态不能为空");
        }

        try {
            DigitalVoiceSystemPO voice = new DigitalVoiceSystemPO();
            voice.setVoiceId(voiceId);
            voice.setStatus(status);

            return this.update(voice,
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getVoiceId, voiceId)
            );
        } catch (Exception e) {
            log.error("更新音色状态失败，voiceId: {}, status: {}", voiceId, status, e);
            throw new RuntimeException("更新音色状态失败", e);
        }
    }

    /**
     * 更新音色推荐状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVoiceRecommendStatus(String voiceId, Integer isRecommended) {
        if (!StringUtils.hasText(voiceId) || isRecommended == null) {
            throw new IllegalArgumentException("音色ID和推荐状态不能为空");
        }

        try {
            DigitalVoiceSystemPO voice = new DigitalVoiceSystemPO();
            voice.setVoiceId(voiceId);
            voice.setIsRecommended(isRecommended);

            return this.update(voice,
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getVoiceId, voiceId)
            );
        } catch (Exception e) {
            log.error("更新音色推荐状态失败，voiceId: {}, isRecommended: {}", voiceId, isRecommended, e);
            throw new RuntimeException("更新音色推荐状态失败", e);
        }
    }

    /**
     * 获取音色统计信息
     */
    @Override
    public Map<String, Object> getVoiceStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 总音色数
            long totalCount = this.count(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getStatus, 1)
            );
            statistics.put("totalCount", totalCount);

            // 推荐音色数
            long recommendedCount = this.count(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getStatus, 1)
                    .eq(DigitalVoiceSystemPO::getIsRecommended, 1)
            );
            statistics.put("recommendedCount", recommendedCount);

            // 按供应商统计
            List<DigitalVoiceSystemPO> allVoices = this.list(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .eq(DigitalVoiceSystemPO::getStatus, 1)
            );

            Map<String, Long> providerStats = allVoices.stream()
                .collect(Collectors.groupingBy(
                    DigitalVoiceSystemPO::getProvider,
                    Collectors.counting()
                ));
            statistics.put("providerStats", providerStats);

            // 按语言统计
            Map<String, Long> languageStats = allVoices.stream()
                .filter(voice -> StringUtils.hasText(voice.getLanguage()))
                .collect(Collectors.groupingBy(
                    DigitalVoiceSystemPO::getLanguage,
                    Collectors.counting()
                ));
            statistics.put("languageStats", languageStats);

            // 按性别统计
            Map<String, Long> genderStats = allVoices.stream()
                .filter(voice -> StringUtils.hasText(voice.getGender()))
                .collect(Collectors.groupingBy(
                    DigitalVoiceSystemPO::getGender,
                    Collectors.counting()
                ));
            statistics.put("genderStats", genderStats);

            return statistics;
        } catch (Exception e) {
            log.error("获取音色统计信息失败", e);
            throw new RuntimeException("获取音色统计信息失败", e);
        }
    }

    /**
     * 根据维度统计音色数量
     */
    @Override
    public Map<String, Map<String, Integer>> getVoiceCountByDimensions() {
        try {
            Map<String, List<VoiceClassifyTagVO>> tagsByDimension = digitalVoiceClassifyTagService.listTagsGroupByDimension();
            Map<String, Map<String, Integer>> result = new HashMap<>();

            for (Map.Entry<String, List<VoiceClassifyTagVO>> entry : tagsByDimension.entrySet()) {
                String dimensionCode = entry.getKey();
                List<VoiceClassifyTagVO> tags = entry.getValue();

                Map<String, Integer> tagCounts = new HashMap<>();
                for (VoiceClassifyTagVO tag : tags) {
                    Integer count = digitalVoiceClassifyTagService.countVoicesByTag(tag.getId());
                    tagCounts.put(tag.getTagCode(), count);
                }

                result.put(dimensionCode, tagCounts);
            }

            return result;
        } catch (Exception e) {
            log.error("根据维度统计音色数量失败", e);
            throw new RuntimeException("根据维度统计音色数量失败", e);
        }
    }

    // ========================================
    // 私有辅助方法
    // ========================================

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<DigitalVoiceSystemPO> buildQueryWrapper(
            Map<String, List<String>> dimensionConditions,
            String keyword,
            List<String> providers,
            List<String> languages,
            List<String> genders,
            List<Long> syncIds,
            List<String> thirdPartyVoiceIds,
            String queryLogic) {

        LambdaQueryWrapper<DigitalVoiceSystemPO> queryWrapper = new LambdaQueryWrapper<DigitalVoiceSystemPO>()
            .eq(DigitalVoiceSystemPO::getStatus, 1);

        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like(DigitalVoiceSystemPO::getVoiceName, keyword)
                .or()
                .like(DigitalVoiceSystemPO::getDescription, keyword)
            );
        }

        // 供应商筛选
        if (providers != null && !providers.isEmpty()) {
            queryWrapper.in(DigitalVoiceSystemPO::getProvider, providers);
        }

        // 语言筛选
        if (languages != null && !languages.isEmpty()) {
            queryWrapper.in(DigitalVoiceSystemPO::getLanguage, languages);
        }

        // 性别筛选
        if (genders != null && !genders.isEmpty()) {
            queryWrapper.in(DigitalVoiceSystemPO::getGender, genders);
        }

        // 同步表ID筛选
        if (syncIds != null && !syncIds.isEmpty()) {
            queryWrapper.in(DigitalVoiceSystemPO::getSyncId, syncIds);
        }

        // 第三方音色ID筛选
        if (thirdPartyVoiceIds != null && !thirdPartyVoiceIds.isEmpty()) {
            queryWrapper.in(DigitalVoiceSystemPO::getThirdPartyVoiceId, thirdPartyVoiceIds);
        }

        // 维度条件筛选
        if (dimensionConditions != null && !dimensionConditions.isEmpty()) {
            List<String> voiceIds = getVoiceIdsByDimensionConditions(dimensionConditions, queryLogic);
            if (!voiceIds.isEmpty()) {
                queryWrapper.in(DigitalVoiceSystemPO::getVoiceId, voiceIds);
            } else {
                // 如果没有匹配的音色，返回空结果
                queryWrapper.eq(DigitalVoiceSystemPO::getVoiceId, "NONE");
            }
        }

        queryWrapper.orderByDesc(DigitalVoiceSystemPO::getSortWeight)
            .orderByDesc(DigitalVoiceSystemPO::getCreatedTime);

        return queryWrapper;
    }

    /**
     * 根据维度条件获取音色ID列表
     */
    private List<String> getVoiceIdsByDimensionConditions(Map<String, List<String>> dimensionConditions, String queryLogic) {
        if (dimensionConditions == null || dimensionConditions.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<String> allVoiceIds = new ArrayList<>();
            boolean isFirstDimension = true;

            for (Map.Entry<String, List<String>> entry : dimensionConditions.entrySet()) {
                String dimensionCode = entry.getKey();
                List<String> tagCodes = entry.getValue();

                if (tagCodes == null || tagCodes.isEmpty()) {
                    continue;
                }

                // 根据标签编码获取标签ID
                List<Long> tagIds = new ArrayList<>();
                for (String tagCode : tagCodes) {
                    VoiceClassifyTagVO tag = digitalVoiceClassifyTagService.getTagByCode(tagCode);
                    if (tag != null) {
                        tagIds.add(tag.getId());
                    }
                }

                if (tagIds.isEmpty()) {
                    continue;
                }

                // 获取该维度下的音色ID列表
                List<String> dimensionVoiceIds = getVoiceIdsByTags(tagIds, "OR"); // 同维度内使用OR

                if (isFirstDimension) {
                    allVoiceIds.addAll(dimensionVoiceIds);
                    isFirstDimension = false;
                } else {
                    if ("AND".equalsIgnoreCase(queryLogic)) {
                        // 跨维度AND：取交集
                        allVoiceIds.retainAll(dimensionVoiceIds);
                    } else {
                        // 跨维度OR：取并集
                        allVoiceIds.addAll(dimensionVoiceIds);
                        allVoiceIds = allVoiceIds.stream().distinct().collect(Collectors.toList());
                    }
                }
            }

            return allVoiceIds;
        } catch (Exception e) {
            log.error("根据维度条件获取音色ID列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据标签获取音色ID列表
     */
    private List<String> getVoiceIdsByTags(List<Long> tagIds, String queryLogic) {
        if (tagIds == null || tagIds.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 先根据tagIds获取tagCodes
            List<VoiceClassifyTagVO> tags = digitalVoiceClassifyTagService.getTagsByIds(tagIds);
            List<String> tagCodes = tags.stream()
                .map(VoiceClassifyTagVO::getTagCode)
                .collect(Collectors.toList());

            if (tagCodes.isEmpty()) {
                return new ArrayList<>();
            }

            List<DigitalVoiceTagRelationPO> relations = digitalVoiceTagRelationMapper.selectList(
                new LambdaQueryWrapper<DigitalVoiceTagRelationPO>()
                    .in(DigitalVoiceTagRelationPO::getTagCode, tagCodes)
            );

            if ("AND".equalsIgnoreCase(queryLogic)) {
                // AND逻辑：音色必须包含所有标签
                Map<String, Long> voiceTagCounts = relations.stream()
                    .collect(Collectors.groupingBy(
                        DigitalVoiceTagRelationPO::getVoiceId,
                        Collectors.counting()
                    ));

                return voiceTagCounts.entrySet().stream()
                    .filter(entry -> entry.getValue() >= tagCodes.size())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            } else {
                // OR逻辑：音色包含任一标签即可
                return relations.stream()
                    .map(DigitalVoiceTagRelationPO::getVoiceId)
                    .distinct()
                    .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("根据标签获取音色ID列表失败，tagIds: {}", tagIds, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据标签编码获取音色ID列表
     */
    private List<String> getVoiceIdsByTagCodes(List<String> tagCodes, String queryLogic) {
        if (tagCodes == null || tagCodes.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<DigitalVoiceTagRelationPO> relations = digitalVoiceTagRelationMapper.selectList(
                new LambdaQueryWrapper<DigitalVoiceTagRelationPO>()
                    .in(DigitalVoiceTagRelationPO::getTagCode, tagCodes)
            );

            if ("AND".equalsIgnoreCase(queryLogic)) {
                // AND逻辑：音色必须包含所有标签
                Map<String, Long> voiceTagCounts = relations.stream()
                    .collect(Collectors.groupingBy(
                        DigitalVoiceTagRelationPO::getVoiceId,
                        Collectors.counting()
                    ));

                return voiceTagCounts.entrySet().stream()
                    .filter(entry -> entry.getValue() >= tagCodes.size())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            } else {
                // OR逻辑：音色包含任一标签即可
                return relations.stream()
                    .map(DigitalVoiceTagRelationPO::getVoiceId)
                    .distinct()
                    .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("根据标签编码获取音色ID列表失败，tagCodes: {}", tagCodes, e);
            return new ArrayList<>();
        }
    }

    /**
     * PO转VO并加载标签信息
     */
    private DigitalVoiceSystemVO convertToVOWithTags(DigitalVoiceSystemPO po) {
        if (po == null) {
            return null;
        }

        DigitalVoiceSystemVO vo = new DigitalVoiceSystemVO();

        try {
            // 使用BeanUtils.copyProperties进行字段复制
            // 注意：PO中的id字段不会复制到VO中，因为VO中没有对应字段
            // 这是预期行为，因为VO不需要暴露数据库主键
            BeanUtils.copyProperties(po, vo);

            // 验证关键字段是否正确复制
            if (log.isDebugEnabled()) {
                validateFieldMapping(po, vo);
                // log.debug("数据转换验证通过 - voiceId: {}, syncId: {}, thirdPartyVoiceId: {}",
                //     vo.getVoiceId(), vo.getSyncId(), vo.getThirdPartyVoiceId());
            }
        } catch (Exception e) {
            log.error("PO到VO数据转换失败，voiceId: {}", po.getVoiceId(), e);
            throw new RuntimeException("数据转换失败", e);
        }

        try {
            // 加载标签信息
            List<DigitalVoiceTagRelationPO> relations = digitalVoiceTagRelationMapper.selectList(
                new LambdaQueryWrapper<DigitalVoiceTagRelationPO>()
                    .eq(DigitalVoiceTagRelationPO::getVoiceId, po.getVoiceId())
            );

            if (!relations.isEmpty()) {
                List<String> tagCodes = relations.stream()
                    .map(DigitalVoiceTagRelationPO::getTagCode)
                    .collect(Collectors.toList());

                // 根据tagCode获取标签信息
                List<VoiceClassifyTagVO> tags = tagCodes.stream()
                    .map(digitalVoiceClassifyTagService::getTagByCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                vo.setTagList(tags);

                // 按维度分组标签
                Map<String, List<VoiceClassifyTagVO>> tagsByDimension = tags.stream()
                    .collect(Collectors.groupingBy(VoiceClassifyTagVO::getDimensionCode));

                List<VoiceDimensionTagGroupVO> dimensionTagGroups = tagsByDimension.entrySet().stream()
                    .map(entry -> {
                        VoiceDimensionTagGroupVO group = new VoiceDimensionTagGroupVO();
                        group.setDimensionCode(entry.getKey());
                        group.setTags(entry.getValue());
                        group.setTagCount(entry.getValue().size());
                        return group;
                    })
                    .collect(Collectors.toList());

                vo.setDimensionTagGroups(dimensionTagGroups);
            } else {
                vo.setTagList(new ArrayList<>());
                vo.setDimensionTagGroups(new ArrayList<>());
            }
        } catch (Exception e) {
            log.warn("加载音色标签信息失败，voiceId: {}", po.getVoiceId(), e);
            vo.setTagList(new ArrayList<>());
            vo.setDimensionTagGroups(new ArrayList<>());
        }

        return vo;
    }

    /**
     * 验证PO到VO的字段映射完整性
     * 用于确保BeanUtils.copyProperties正确处理所有字段
     */
    private void validateFieldMapping(DigitalVoiceSystemPO po, DigitalVoiceSystemVO vo) {
        if (po == null || vo == null) {
            return;
        }

        // 验证关键业务字段是否正确映射
        assert Objects.equals(po.getVoiceId(), vo.getVoiceId()) : "voiceId字段映射失败";
        assert Objects.equals(po.getSyncId(), vo.getSyncId()) : "syncId字段映射失败";
        assert Objects.equals(po.getThirdPartyVoiceId(), vo.getThirdPartyVoiceId()) : "thirdPartyVoiceId字段映射失败";
        assert Objects.equals(po.getProvider(), vo.getProvider()) : "provider字段映射失败";
        assert Objects.equals(po.getVoiceName(), vo.getVoiceName()) : "voiceName字段映射失败";
        assert Objects.equals(po.getDescription(), vo.getDescription()) : "description字段映射失败";
        assert Objects.equals(po.getLanguage(), vo.getLanguage()) : "language字段映射失败";
        assert Objects.equals(po.getGender(), vo.getGender()) : "gender字段映射失败";
        assert Objects.equals(po.getDemoAudioUrl(), vo.getDemoAudioUrl()) : "demoAudioUrl字段映射失败";
        assert Objects.equals(po.getSortWeight(), vo.getSortWeight()) : "sortWeight字段映射失败";
        assert Objects.equals(po.getStatus(), vo.getStatus()) : "status字段映射失败";
        assert Objects.equals(po.getIsRecommended(), vo.getIsRecommended()) : "isRecommended字段映射失败";
        assert Objects.equals(po.getCreatedTime(), vo.getCreatedTime()) : "createdTime字段映射失败";
        assert Objects.equals(po.getUpdateTime(), vo.getUpdateTime()) : "updateTime字段映射失败";

        log.debug("字段映射验证通过，voiceId: {}", po.getVoiceId());
    }
}
