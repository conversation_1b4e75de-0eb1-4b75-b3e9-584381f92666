# ==================== 生产环境配置 ====================
server:
  port: 8818
  tomcat:
    uri-encoding: utf-8
    connection-timeout: 30000                 # 生产环境30秒连接超时
    keep-alive-timeout: 60000                # 生产环境60秒保活超时
    max-connections: 8192                    # 最大连接数
    accept-count: 100                        # 等待队列长度

spring:
  mvc:
    servlet:
      path: /digital
  application:
    name: nacos-digital-server

  # 生产环境数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 生产环境数据库连接信息通过环境变量配置
    url: ${PROD_DB_URL:***********************************************************************************************************************************************************************************************************************}
    username: ${PROD_DB_USERNAME:digital_user}
    password: ${PROD_DB_PASSWORD:digital_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    tomcat:
      max-active: 50                         # 生产环境增加连接池大小
      initial-size: 10                       # 初始连接数
      max-wait: 60000                        # 最大等待时间
      min-idle: 10                           # 最小空闲连接
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

  # 生产环境Redis配置
  data:
    redis:
      host: ${PROD_REDIS_HOST:prod-redis}
      port: ${PROD_REDIS_PORT:6379}
      database: ${PROD_REDIS_DB:0}
      password: ${PROD_REDIS_PASSWORD:redis_password}
      lettuce:
        pool:
          # 生产环境连接池配置
          max-active: 500                    # 最大连接数
          max-idle: 100                      # 最大空闲连接
          min-idle: 20                       # 最小空闲连接
          max-wait: 3000ms                   # 最大等待时间
        timeout: 5000ms                      # 连接超时
        command-timeout: 3000ms              # 命令超时
        shutdown-timeout: 100ms              # 关闭超时

  # 生产环境Nacos配置
  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: ${PROD_NACOS_ADDR:prod-nacos:8848}
        group: ${PROD_NACOS_GROUP:PROD_GROUP}
        namespace: ${PROD_NACOS_NAMESPACE:prod-namespace}
        username: ${PROD_NACOS_USERNAME:nacos}
        password: ${PROD_NACOS_PASSWORD:nacos}
      config:
        enabled: true
        server-addr: ${PROD_NACOS_ADDR:prod-nacos:8848}
        file-extension: yml
        namespace: ${PROD_NACOS_NAMESPACE:prod-namespace}
        username: ${PROD_NACOS_USERNAME:nacos}
        password: ${PROD_NACOS_PASSWORD:nacos}

# 生产环境日志配置
logs:
  path: ${PROD_LOGS_PATH:/app/logs}

# ==================== 生产环境 - 统一任务系统配置 ====================
universal-task:
  # 生产环境状态同步配置
  status:
    sync:
      enabled: true                           # 生产环境启用状态同步
      base-scan-rate: 60000                  # 生产环境1分钟扫描一次，减少负载
      stages:
        stage1-interval-ms: 60000             # 生产环境1分钟检测
        stage1-end-ms: 600000                 # 生产环境10分钟切换到阶段2
        stage2-interval-ms: 120000            # 生产环境2分钟检测，减少API调用
        stage2-end-ms: 3600000                # 生产环境1小时后继续检测
      rate-limit:
        max-calls-per-minute: 100             # 生产环境限制API调用频率
        max-concurrent-per-provider: 3        # 生产环境保守的并发设置
        cache-time-ms: 120000                 # 生产环境2分钟缓存，减少重复查询

  # 生产环境保守的日志配置
  monitoring:
    logging:
      level: "WARN"                          # 生产环境只记录警告和错误
      include-context: false                 # 不包含详细上下文减少日志量
      include-stack-trace: false             # 不包含堆栈信息

  # 生产环境较长的超时时间
  timeout:
    default: 3600000                        # 生产环境默认60分钟超时
    video-translate: 3600000                # 视频翻译60分钟超时
    audio-generate: 600000                  # 音频生成10分钟超时
    video-edit: 7200000                     # 视频编辑120分钟超时

  # 生产环境兜底调度配置
  fallback-scheduler:
    fixed-delay: 300000                     # 5分钟执行一次
    initial-delay: 60000                    # 1分钟后开始执行
    batch-size: 100                         # 每次处理100个任务
    max-retry-hours: 48                     # 最大重试48小时

  # 生产环境监控配置
  monitoring:
    metrics:
      enabled: true
      export-interval: 300000               # 5分钟导出一次指标
    logging:
      enabled: true
      level: "WARN"                         # 只记录警告级别以上日志

  # 生产环境Redis重试策略
  redis:
    retry:
      max-attempts: 5                       # 生产环境增加重试次数
      initial-interval: 2000                # 初始重试间隔2秒
      multiplier: 2.0
      max-interval: 30000                   # 最大重试间隔30秒