package com.nacos.service.scheduler;

import com.nacos.config.UniversalTaskConfig;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.mapper.DigitalAudioTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.VideoTranslateAsyncService;
import com.nacos.service.factory.TaskProcessorFactory;
import com.nacos.service.IDigitalAudioTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UniversalTaskStatusSyncScheduler 集成测试
 * 
 * 验证统一任务状态同步调度器的核心功能：
 * 1. 智能任务状态同步
 * 2. 分阶段查询策略
 * 3. API限流保护
 * 4. 健康检查和统计
 * 5. 异常处理和恢复
 * 
 * 重点验证能够解决"任务在进行中但没有获取结果"的原始问题
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("统一任务状态同步调度器测试")
class UniversalTaskStatusSyncSchedulerTest {

    @Mock
    private TaskProcessorFactory processorFactory;
    
    @Mock
    private VideoTranslateAsyncService videoTranslateAsyncService;
    
    @Mock
    private IDigitalAudioTaskService digitalAudioTaskService;
    
    @Mock
    private UniversalTaskConfig config;
    
    @Mock
    private VideoTranslateTaskMapper videoTranslateTaskMapper;
    
    @Mock
    private DigitalAudioTaskMapper digitalAudioTaskMapper;
    
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    
    @InjectMocks
    private UniversalTaskStatusSyncScheduler scheduler;
    
    private UniversalTaskConfig.StatusConfig statusConfig;
    private UniversalTaskConfig.SyncConfig syncConfig;
    private UniversalTaskConfig.StageConfig stageConfig;
    private UniversalTaskConfig.RateLimitConfig rateLimitConfig;

    @BeforeEach
    void setUp() {
        // 初始化配置对象
        statusConfig = new UniversalTaskConfig.StatusConfig();
        syncConfig = new UniversalTaskConfig.SyncConfig();
        stageConfig = new UniversalTaskConfig.StageConfig();
        rateLimitConfig = new UniversalTaskConfig.RateLimitConfig();
        
        // 设置默认配置值
        syncConfig.setEnabled(true);
        syncConfig.setBatchSize(50);
        syncConfig.setBaseScanRate(30000L);
        syncConfig.setStages(stageConfig);
        syncConfig.setRateLimit(rateLimitConfig);
        
        stageConfig.setStage1IntervalMs(30000L);
        stageConfig.setStage1EndMs(300000L);
        stageConfig.setStage2IntervalMs(60000L);
        stageConfig.setStage2EndMs(1800000L);
        
        rateLimitConfig.setMaxCallsPerMinute(150);
        rateLimitConfig.setMaxConcurrentPerProvider(5);
        rateLimitConfig.setCacheTimeMs(60000L);
        
        statusConfig.setSync(syncConfig);
        
        // 配置mock返回
        when(config.getStatus()).thenReturn(statusConfig);
    }

    @Test
    @DisplayName("测试调度器初始化和配置加载")
    void testSchedulerInitialization() {
        // 验证配置正确加载
        assertNotNull(scheduler);
        
        // 验证健康状态
        Map<String, Object> health = scheduler.getHealthStatus();
        assertNotNull(health);
        assertTrue((Boolean) health.get("enabled"));
        assertTrue((Boolean) health.get("schedulerRunning"));
        
        // 验证统计信息初始化
        Map<String, Object> stats = scheduler.getSyncStatistics();
        assertNotNull(stats);
        assertTrue(stats.containsKey("startupTime"));
        assertTrue(stats.containsKey("currentTime"));
    }

    @Test
    @DisplayName("测试视频翻译任务状态同步流程")
    void testVideoTranslateTaskSync() {
        // 准备测试数据
        VideoTranslateTaskPO task = createVideoTranslateTask();
        List<VideoTranslateTaskPO> tasks = Arrays.asList(task);
        
        // 配置mock行为
        when(videoTranslateTaskMapper.selectList(any())).thenReturn(tasks);
        when(digitalAudioTaskMapper.selectList(any())).thenReturn(Collections.emptyList());
        when(videoTranslateTaskMapper.selectOne(any())).thenReturn(task);
        when(videoTranslateAsyncService.syncTaskStatus(any())).thenReturn(Result.SUCCESS(new HashMap<>()));
        
        // 执行同步
        scheduler.intelligentTaskStatusSync();
        
        // 验证调用
        verify(videoTranslateTaskMapper, times(1)).selectList(any());
        verify(videoTranslateAsyncService, times(1)).syncTaskStatus(any());
        
        // 验证统计信息更新
        Map<String, Object> stats = scheduler.getSyncStatistics();
        assertTrue((Long) stats.getOrDefault("totalScans", 0L) > 0);
    }

    @Test
    @DisplayName("测试音频生成任务超时处理")
    void testAudioTaskTimeoutHandling() {
        // 准备超时的音频任务
        DigitalAudioTaskPO timeoutTask = createAudioTask();
        timeoutTask.setCreatedTime(LocalDateTime.now().minusMinutes(15)); // 15分钟前创建
        
        List<DigitalAudioTaskPO> tasks = Arrays.asList(timeoutTask);
        
        // 配置mock行为
        when(videoTranslateTaskMapper.selectList(any())).thenReturn(Collections.emptyList());
        when(digitalAudioTaskMapper.selectList(any())).thenReturn(tasks);
        when(digitalAudioTaskMapper.selectOne(any())).thenReturn(timeoutTask);
        when(digitalAudioTaskService.updateTaskStatus(anyString(), any(), anyString())).thenReturn(true);
        
        // 执行同步
        scheduler.intelligentTaskStatusSync();
        
        // 验证超时任务被标记为失败
        verify(digitalAudioTaskService, times(1)).updateTaskStatus(
            eq(timeoutTask.getTaskId()),
            eq(UnifiedTaskStatusEnum.FAILED.getCode()),
            contains("超时")
        );
        
        // 验证统计信息
        Map<String, Object> execStats = scheduler.getExecutionStatistics();
        assertTrue((Long) execStats.getOrDefault("timeoutTasksMarked", 0L) > 0);
    }

    @Test
    @DisplayName("测试分阶段查询策略")
    void testStageBasedQueryStrategy() {
        // 使用反射访问私有方法进行测试
        UniversalTaskStatusSyncScheduler.TaskSyncCandidate newTask = 
            new UniversalTaskStatusSyncScheduler.TaskSyncCandidate(
                "task1", "provider1", System.currentTimeMillis() - 60000, TaskType.VIDEO_TRANSLATE); // 1分钟前
                
        UniversalTaskStatusSyncScheduler.TaskSyncCandidate oldTask = 
            new UniversalTaskStatusSyncScheduler.TaskSyncCandidate(
                "task2", "provider1", System.currentTimeMillis() - 600000, TaskType.VIDEO_TRANSLATE); // 10分钟前
        
        // 通过反射测试私有方法
        try {
            java.lang.reflect.Method shouldSyncMethod = 
                UniversalTaskStatusSyncScheduler.class.getDeclaredMethod("shouldSyncNow", 
                    UniversalTaskStatusSyncScheduler.TaskSyncCandidate.class);
            shouldSyncMethod.setAccessible(true);
            
            // 新任务应该在阶段1策略下同步
            boolean newTaskShouldSync = (Boolean) shouldSyncMethod.invoke(scheduler, newTask);
            
            // 老任务应该在阶段2策略下同步
            boolean oldTaskShouldSync = (Boolean) shouldSyncMethod.invoke(scheduler, oldTask);
            
            // 验证分阶段策略生效（具体结果取决于当前时间和配置）
            assertNotNull(newTaskShouldSync);
            assertNotNull(oldTaskShouldSync);
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试API限流保护机制")
    void testRateLimitProtection() {
        // 准备大量任务触发限流
        List<VideoTranslateTaskPO> manyTasks = new ArrayList<>();
        for (int i = 0; i < 200; i++) {
            VideoTranslateTaskPO task = createVideoTranslateTask();
            task.setTaskId("task_" + i);
            manyTasks.add(task);
        }
        
        // 配置mock行为
        when(videoTranslateTaskMapper.selectList(any())).thenReturn(manyTasks);
        when(digitalAudioTaskMapper.selectList(any())).thenReturn(Collections.emptyList());
        when(videoTranslateTaskMapper.selectOne(any())).thenReturn(manyTasks.get(0));
        when(videoTranslateAsyncService.syncTaskStatus(any())).thenReturn(Result.SUCCESS(new HashMap<>()));
        
        // 执行同步
        scheduler.intelligentTaskStatusSync();
        
        // 验证限流统计
        Map<String, Object> execStats = scheduler.getExecutionStatistics();
        // 由于限流，实际同步的任务数应该少于总任务数
        Long totalSynced = (Long) execStats.getOrDefault("totalSynced", 0L);
        assertTrue(totalSynced < manyTasks.size(), "限流应该生效，实际同步数应少于总任务数");
    }

    @Test
    @DisplayName("测试异常处理和错误统计")
    void testExceptionHandlingAndErrorStats() {
        // 准备测试数据
        VideoTranslateTaskPO task = createVideoTranslateTask();
        List<VideoTranslateTaskPO> tasks = Arrays.asList(task);
        
        // 配置mock抛出异常
        when(videoTranslateTaskMapper.selectList(any())).thenReturn(tasks);
        when(digitalAudioTaskMapper.selectList(any())).thenReturn(Collections.emptyList());
        when(videoTranslateTaskMapper.selectOne(any())).thenReturn(task);
        when(videoTranslateAsyncService.syncTaskStatus(any())).thenThrow(new RuntimeException("API调用失败"));
        
        // 执行同步（不应该抛出异常）
        assertDoesNotThrow(() -> scheduler.intelligentTaskStatusSync());
        
        // 验证错误统计
        Map<String, Object> stats = scheduler.getSyncStatistics();
        Long errorCount = (Long) stats.getOrDefault("errorCount", 0L);
        assertTrue(errorCount > 0, "应该记录错误次数");
        
        // 验证健康状态受影响
        Map<String, Object> health = scheduler.getHealthStatus();
        String errorRate = (String) health.get("errorRate");
        assertNotNull(errorRate);
        assertNotEquals("0.00%", errorRate);
    }

    @Test
    @DisplayName("测试统计信息的准确性")
    void testStatisticsAccuracy() {
        // 重置统计信息
        scheduler.resetStatistics();
        
        // 准备测试数据
        VideoTranslateTaskPO videoTask = createVideoTranslateTask();
        DigitalAudioTaskPO audioTask = createAudioTask();
        
        // 配置mock行为
        when(videoTranslateTaskMapper.selectList(any())).thenReturn(Arrays.asList(videoTask));
        when(digitalAudioTaskMapper.selectList(any())).thenReturn(Arrays.asList(audioTask));
        when(videoTranslateTaskMapper.selectOne(any())).thenReturn(videoTask);
        when(digitalAudioTaskMapper.selectOne(any())).thenReturn(audioTask);
        when(videoTranslateAsyncService.syncTaskStatus(any())).thenReturn(Result.SUCCESS(new HashMap<>()));
        when(digitalAudioTaskService.updateTaskStatus(anyString(), any(), anyString())).thenReturn(true);
        
        // 执行多次同步
        scheduler.intelligentTaskStatusSync();
        scheduler.intelligentTaskStatusSync();
        
        // 验证统计信息
        Map<String, Object> stats = scheduler.getSyncStatistics();
        assertEquals(2L, stats.get("totalScans"));
        assertTrue((Long) stats.get("totalCandidates") >= 2);
        
        Map<String, Object> execStats = scheduler.getExecutionStatistics();
        assertTrue((Long) execStats.getOrDefault("videoTranslateTasksSynced", 0L) > 0);
        assertTrue((Long) execStats.getOrDefault("audioGenerateTasksSynced", 0L) > 0);
    }

    @Test
    @DisplayName("测试健康检查功能")
    void testHealthCheckFunctionality() {
        // 测试健康状态
        assertTrue(scheduler.isHealthy());
        
        Map<String, Object> health = scheduler.getHealthStatus();
        
        // 验证基本健康指标
        assertTrue((Boolean) health.get("enabled"));
        assertTrue((Boolean) health.get("schedulerRunning"));
        assertNotNull(health.get("startupTime"));
        assertNotNull(health.get("uptime"));
        
        // 验证组件状态
        assertNotNull(health.get("globalRateLimiterInitialized"));
        assertNotNull(health.get("providerRateLimitersCount"));
        assertNotNull(health.get("cacheSize"));
        
        // 验证健康判断逻辑
        assertTrue((Boolean) health.get("healthy"));
    }

    @Test
    @DisplayName("端到端测试：任务状态同步完整流程")
    void testEndToEndTaskStatusSync() {
        // 模拟完整的任务状态同步流程
        
        // 1. 创建处理中的任务
        VideoTranslateTaskPO processingTask = createVideoTranslateTask();
        processingTask.setStatus(UnifiedTaskStatusEnum.PROGRESS.getCode());
        
        // 2. 配置mock：任务查询返回处理中任务
        when(videoTranslateTaskMapper.selectList(any())).thenReturn(Arrays.asList(processingTask));
        when(digitalAudioTaskMapper.selectList(any())).thenReturn(Collections.emptyList());
        when(videoTranslateTaskMapper.selectOne(any())).thenReturn(processingTask);
        
        // 3. 配置mock：状态同步返回成功
        Map<String, Object> syncResult = new HashMap<>();
        syncResult.put("status", "completed");
        syncResult.put("result_url", "http://example.com/result.mp4");
        when(videoTranslateAsyncService.syncTaskStatus(any())).thenReturn(Result.SUCCESS(syncResult));
        
        // 4. 执行状态同步
        scheduler.intelligentTaskStatusSync();
        
        // 5. 验证同步流程执行
        verify(videoTranslateTaskMapper, times(1)).selectList(any());
        verify(videoTranslateTaskMapper, times(1)).selectOne(any());
        verify(videoTranslateAsyncService, times(1)).syncTaskStatus(eq(processingTask));
        
        // 6. 验证统计信息更新
        Map<String, Object> stats = scheduler.getSyncStatistics();
        assertTrue((Long) stats.get("totalScans") > 0);
        assertTrue((Long) stats.get("totalSynced") > 0);
        
        Map<String, Object> execStats = scheduler.getExecutionStatistics();
        assertTrue((Long) execStats.get("videoTranslateTasksSynced") > 0);
        
        // 7. 验证原始问题得到解决：任务状态能够从processing更新为completed
        // 这里通过验证syncTaskStatus被正确调用来确认状态同步机制工作正常
        assertTrue(true, "端到端测试通过，状态同步机制正常工作");
    }

    // 辅助方法：创建测试用的视频翻译任务
    private VideoTranslateTaskPO createVideoTranslateTask() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setTaskId("test_video_task_" + System.currentTimeMillis());
        task.setUserId("test_user");
        task.setProvider("LINGYANG");
        task.setStatus(UnifiedTaskStatusEnum.PROGRESS.getCode());
        task.setCreatedTime(LocalDateTime.now().minusMinutes(2));
        return task;
    }
    
    // 辅助方法：创建测试用的音频生成任务
    private DigitalAudioTaskPO createAudioTask() {
        DigitalAudioTaskPO task = new DigitalAudioTaskPO();
        task.setTaskId("test_audio_task_" + System.currentTimeMillis());
        task.setUserId("test_user");
        task.setProvider("MINIMAX");
        task.setStatus(UnifiedTaskStatusEnum.PROGRESS.getCode());
        task.setCreatedTime(LocalDateTime.now().minusMinutes(5));
        return task;
    }
}
